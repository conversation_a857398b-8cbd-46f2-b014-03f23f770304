<template>
  <div class="policy-container app-container">
    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady && tableColumns.length > 0" :columns="tableColumns" :data="policyList"
      :loading="tableLoading" :showIndex="false" :searchColumns="searchableColumns" :showOperation="true"
      operationLabel="操作" operationWidth="200" :fixedOperation="true" ref="tableListRef" @search="handleSearch"
      @reset="resetSearch"
      :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
        <el-button type="primary" class="custom-btn" @click="handleRefresh">刷新</el-button>
      </template>

      <!-- 政策类型列插槽 -->
      <template #policyType="{ row }">
        <el-tag :type="getTypeTagType(row.policyType)" size="small">
          {{ row.policyType }}
        </el-tag>
      </template>

      <!-- 政策状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="row.status === '0' ? 'success' : 'danger'" size="small">
          {{ row.status === '0' ? '正常' : '停用' }}
        </el-tag>
      </template>

      <!-- 申请状态列插槽 -->
      <template #applicationStatus="{ row }">
        <el-tag v-if="row.userApplication" :type="getApplicationStatusTagType(row.applicationStatus)" size="small">
          {{ getApplicationStatusText(row.applicationStatus) }}
        </el-tag>
        <span v-else>未申请</span>
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <!-- 未申请过或申请被拒绝，可以申请 -->
          <el-button v-if="row.canApply" type="primary" link @click="handleApply(row)" :disabled="row.status !== '0'">
            {{ row.userApplication ? '重新申请' : '立即申请' }}
          </el-button>

          <!-- 查看申请材料 -->
          <el-button v-if="row.userApplication" type="success" link @click="handleViewMaterials(row.userApplication)">
            查看申请
          </el-button>

          <!-- 查看审核状态 -->
          <el-button v-if="row.userApplication" type="info" link
            @click="handleViewApprovalRecords(row.userApplication)">
            审核状态
          </el-button>
        </div>
      </template>
    </TableList>
    <div v-else class="loading-placeholder">
      <el-empty description="正在加载表格配置..."></el-empty>
    </div>
    <!-- 申请弹窗 -->
    <el-dialog v-model="applyDialogVisible" :title="`申请 - ${currentPolicy?.policyName}`" width="1000px"
      :close-on-click-modal="false" append-to-body>
      <div class="apply-form">
        <el-form ref="applyFormRef" :model="applyForm" :rules="applyRules" label-width="120px">
          <el-form-item label="申请政策" prop="policyId">
            <el-input v-model="currentPolicy.policyName" disabled />
          </el-form-item>

          <el-divider content-position="left">申请人信息</el-divider>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="申请人姓名" prop="applicantName">
                <el-input v-model="applyForm.applicantName" placeholder="请输入申请人姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="applicantPhone">
                <el-input v-model="applyForm.applicantPhone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">企业基本信息</el-divider>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="企业名称" prop="companyName">
                <el-input v-model="applyForm.companyName" placeholder="请输入企业名称" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="统一社会信用代码" prop="companyCode">
                <el-input v-model="applyForm.companyCode" placeholder="请输入统一社会信用代码" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="法定代表人" prop="companyLegalPerson">
                <el-input v-model="applyForm.companyLegalPerson" placeholder="请输入法定代表人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业联系人" prop="companyContactPerson">
                <el-input v-model="applyForm.companyContactPerson" placeholder="请输入企业联系人" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="企业联系电话" prop="companyContactPhone">
                <el-input v-model="applyForm.companyContactPhone" placeholder="请输入企业联系电话" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业注册地址" prop="companyAddress">
                <el-input v-model="applyForm.companyAddress" placeholder="请输入企业注册地址" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">银行对公户信息</el-divider>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="开户银行" prop="bankName">
                <el-input v-model="applyForm.bankName" placeholder="请输入开户银行名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="账户名称" prop="bankAccountName">
                <el-input v-model="applyForm.bankAccountName" placeholder="请输入银行账户名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="银行账号" prop="bankAccountNumber">
                <el-input v-model="applyForm.bankAccountNumber" placeholder="请输入银行账号" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="备注" prop="remark">
            <el-input v-model="applyForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>

          <el-divider content-position="left">所需材料</el-divider>

          <div class="required-materials">
            <div class="material-item" v-for="(material, index) in requiredMaterials" :key="index">
              <div class="material-header">
                <div class="material-info">
                  <el-icon class="material-icon">
                    <Document />
                  </el-icon>
                  <span class="material-name">{{ material.name }}</span>
                  <div class="material-tags">
                    <el-tag v-if="material.required" type="danger" size="small">必需</el-tag>
                    <el-tag v-else type="info" size="small">可选</el-tag>
                  </div>
                </div>
              </div>

              <div class="material-upload">
                <FileUpload v-model:value="material.files" :limit="5" :file-size="0" :file-type="[]"
                  :is-show-tip="false" @fileLoad="(data) => handleFileLoad(data, index)" />
              </div>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="applyDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmitApply">
            提交申请
          </el-button>
        </div>
      </template>
    </el-dialog>



    <!-- 材料查看弹窗 -->
    <MaterialsDialog ref="materialsDialogRef" />

    <!-- 审核记录弹窗 -->
    <ApprovalRecordsDialog ref="approvalRecordsDialogRef" />
  </div>
</template>

<script setup name="PolicyS">
import { ref, reactive, onMounted, getCurrentInstance, toRefs, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { listPolicyInfo } from "@/api/policy/info"
import { addPolicyApplication, listMyApplications } from "@/api/policy/application"
import { parseTime } from "@/utils/ruoyi"
import { Document } from '@element-plus/icons-vue'
import FileUpload from '@/components/FileUpload/index.vue'
import MaterialsDialog from '../policyPlan/MaterialsDialog.vue'
import ApprovalRecordsDialog from '../policyPlan/ApprovalRecordsDialog.vue'
import TableList from '@/components/TableList/index.vue'
import useUserStore from '@/store/modules/user'
import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 表格相关数据
const policyList = ref([])
const loading = ref(true)
const total = ref(0)
const tableColumns = ref([])
const searchableColumns = ref([])
const tableLoading = ref(false)
const isTableReady = ref(true)
const tableListRef = ref(null)

// 申请弹窗相关数据
const applyDialogVisible = ref(false)
const currentPolicy = ref(null)
const submitLoading = ref(false)
const applyFormRef = ref(null)
const materialsDialogRef = ref(null)
const approvalRecordsDialogRef = ref(null)

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    policyName: undefined,
    policyType: undefined,
    status: undefined
  }
})

const { queryParams } = toRefs(data)

// 申请表单
const applyForm = reactive({
  policyId: '',
  applicantName: '',
  applicantPhone: '',
  companyName: '',
  companyCode: '',
  companyLegalPerson: '',
  companyAddress: '',
  companyContactPerson: '',
  companyContactPhone: '',
  bankName: '',
  bankAccountName: '',
  bankAccountNumber: '',
  remark: ''
})

// 表单验证规则
const applyRules = {
  applicantName: [
    { required: true, message: '请输入申请人姓名', trigger: 'blur' }
  ],
  applicantPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  companyName: [
    { required: true, message: '请输入企业名称', trigger: 'blur' }
  ],
  companyCode: [
    { required: true, message: '请输入统一社会信用代码', trigger: 'blur' }
  ],
  companyLegalPerson: [
    { required: true, message: '请输入法定代表人', trigger: 'blur' }
  ],
  companyAddress: [
    { required: true, message: '请输入企业注册地址', trigger: 'blur' }
  ],
  companyContactPerson: [
    { required: true, message: '请输入企业联系人', trigger: 'blur' }
  ],
  companyContactPhone: [
    { required: true, message: '请输入企业联系电话', trigger: 'blur' }
  ],
  bankName: [
    { required: true, message: '请输入开户银行名称', trigger: 'blur' }
  ],
  bankAccountName: [
    { required: true, message: '请输入银行账户名称', trigger: 'blur' }
  ],
  bankAccountNumber: [
    { required: true, message: '请输入银行账号', trigger: 'blur' }
  ]
}

// 所需材料列表
const requiredMaterials = ref([
  {
    name: '《企业新增岗位吸纳就业困难人员、高校毕业生和退役军人社保补贴申领表》',
    required: true,
    files: []
  },
  {
    name: '企业的营业执照副本复印件',
    required: true,
    files: []
  },
  {
    name: '退役军人需提供退伍证原件及复印件',
    required: false,
    files: []
  },
  {
    name: '企业社保缴费凭证',
    required: true,
    files: []
  }
])

// 表格列配置
const initTableColumns = () => {
  tableColumns.value = [
    {
      prop: 'policyName',
      label: '政策名称',
      minWidth: 200
    },
    {
      prop: 'policyType',
      label: '政策类型',
      width: 120,
      tableSlot: true
    },
    {
      prop: 'policyDescription',
      label: '政策描述',
      width: 300
    },
    {
      prop: 'effectiveDate',
      label: '生效日期',
      width: 120
    },
    {
      prop: 'status',
      label: '政策状态',
      width: 100,
      tableSlot: true
    },
    {
      prop: 'applicationStatus',
      label: '申请状态',
      width: 120,
      tableSlot: true
    }
  ]

  searchableColumns.value = [
    {
      prop: 'policyName',
      label: '政策名称',
      type: 'input'
    }
  ]
}

// 检查是否需要强制退出登录（清除其他域传递的退出请求）
const checkForceLogoutParam = async () => {
  const forceLogoutParam = route.query.forceLogout
  if (forceLogoutParam === 'true') {
    try {
      console.log('收到跨域退出登录请求，正在清除当前域的token...')

      // 强制执行退出登录，清除当前域（管理平台域）的token
      await userStore.logOut()
      ElMessage.success('已清除登录状态，请重新登录')

      // 清除URL中的forceLogout参数，避免重复执行
      const newQuery = { ...route.query }
      delete newQuery.forceLogout

      // 跳转到登录页面，保留原始目标路径
      router.replace({
        path: '/login',
        query: {
          redirect: route.path,
          ...newQuery // 保留其他查询参数
        }
      })
    } catch (error) {
      console.error('强制退出登录失败:', error)
      ElMessage.error('清除登录状态失败，请手动退出登录')

      // 即使退出登录API失败，也要跳转到登录页面
      router.replace({
        path: '/login',
        query: {
          redirect: route.path
        }
      })
    }
  }
}

// 页面初始化
onMounted(async () => {
  // 先检查是否需要强制退出登录（处理跨域退出请求）
  await checkForceLogoutParam()

  // 如果没有强制退出登录，则正常初始化页面
  if (route.query.forceLogout !== 'true') {
    initTableColumns()
    getList()
  }
})



// 表格相关处理函数
const handleSearch = (params) => {
  Object.assign(queryParams.value, params)
  queryParams.value.pageNum = 1
  getList()
}

const resetSearch = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    policyName: undefined,
    policyType: undefined,
    status: undefined
  }
  getList()
}

const handleCurrentChange = (page) => {
  queryParams.value.pageNum = page
  getList()
}

const handleSizeChange = (size) => {
  queryParams.value.pageSize = size
  queryParams.value.pageNum = 1
  getList()
}

// 获取政策列表
const getList = async () => {
  loading.value = true
  tableLoading.value = true
  try {
    // 获取政策列表
    const policyResponse = await listPolicyInfo({
      ...queryParams.value,
      status: '0' // 只查询正常状态的政策
    })

    // 获取我的申请记录
    const applicationResponse = await listMyApplications({})

    const policies = policyResponse.rows || []
    const applications = applicationResponse.rows || []
    total.value = policyResponse.total || 0

    console.log('政策列表数据:', policies)
    console.log('申请记录数据:', applications)

    // 为每个政策添加申请状态信息
    policyList.value = policies.map(policy => {
      const userApplication = applications.find(app => app.policyId === policy.policyId)
      return {
        ...policy,
        userApplication: userApplication || null,
        applicationStatus: userApplication ? userApplication.applicationStatus : null,
        canApply: !userApplication || ['2', '5'].includes(userApplication.applicationStatus)
      }
    })

    console.log('最终政策列表:', policyList.value)

  } catch (error) {
    console.error('获取政策列表失败:', error)
    proxy?.$modal?.msgError('获取政策列表失败')
  } finally {
    loading.value = false
    tableLoading.value = false
  }
}

const handleSelectionChange = () => {
  // 处理选择变化
}

const handleRefresh = () => {
  getList()
}



// 获取政策类型标签类型
const getTypeTagType = (type) => {
  const typeMap = {
    '就业扶持': 'success',
    '创业支持': 'warning',
    '技能培训': 'info',
    '社会保障': 'primary',
    '其他': 'default'
  }
  return typeMap[type] || 'default'
}

// 获取申请状态标签类型
const getApplicationStatusTagType = (status) => {
  const statusMap = {
    '0': 'warning',  // 待初审
    '1': 'success',  // 初审通过
    '2': 'danger',   // 初审拒绝
    '3': 'warning',  // 待终审
    '4': 'success',  // 终审通过
    '5': 'danger',   // 终审拒绝
    '6': 'success'   // 已完成
  }
  return statusMap[status] || 'info'
}

// 获取申请状态文本
const getApplicationStatusText = (status) => {
  const statusMap = {
    '0': '待初审',
    '1': '初审通过',
    '2': '初审拒绝',
    '3': '待终审',
    '4': '终审通过',
    '5': '终审拒绝',
    '6': '已完成'
  }
  return statusMap[status] || '未知状态'
}



// 处理申请操作
const handleApply = (policy) => {
  currentPolicy.value = policy
  applyForm.policyId = policy.policyId
  resetMaterials()
  initMaterials()
  applyDialogVisible.value = true
}



// 查看申请材料
const handleViewMaterials = (application) => {
  materialsDialogRef.value?.openDialog(application)
}

// 查看审核记录
const handleViewApprovalRecords = (application) => {
  approvalRecordsDialogRef.value?.openDialog(application.applicationId)
}

// 重置材料上传状态
const resetMaterials = () => {
  requiredMaterials.value.forEach(material => {
    material.files = []
  })
}

// 初始化材料状态
const initMaterials = () => {
  requiredMaterials.value.forEach(material => {
    if (!material.files) {
      material.files = []
    }
  })
}

// 文件上传处理
const handleFileLoad = (data, index) => {
  requiredMaterials.value[index].files = data.fileList || []
}

// 提交申请
const handleSubmitApply = async () => {
  if (!applyFormRef.value) return

  try {
    await applyFormRef.value.validate()

    // 检查必需材料是否已上传
    const requiredNotUploaded = requiredMaterials.value.filter(m => m.required && (!m.files || m.files.length === 0))
    if (requiredNotUploaded.length > 0) {
      proxy?.$modal?.msgError('请上传所有必需的材料文件')
      return
    }

    submitLoading.value = true

    // 准备申请数据
    const applicationData = {
      policyId: applyForm.policyId,
      applicantName: applyForm.applicantName,
      applicantPhone: applyForm.applicantPhone,
      companyName: applyForm.companyName,
      companyCode: applyForm.companyCode,
      companyLegalPerson: applyForm.companyLegalPerson,
      companyAddress: applyForm.companyAddress,
      companyContactPerson: applyForm.companyContactPerson,
      companyContactPhone: applyForm.companyContactPhone,
      bankName: applyForm.bankName,
      bankAccountName: applyForm.bankAccountName,
      bankAccountNumber: applyForm.bankAccountNumber,
      // applicantUserId 由后端根据当前登录用户自动设置
      requiredMaterials: JSON.stringify(requiredMaterials.value),
      remark: applyForm.remark
    }

    await addPolicyApplication(applicationData)

    proxy?.$modal?.msgSuccess('申请提交成功，请等待审核')
    applyDialogVisible.value = false
    resetForm()
    // 重新加载页面数据以更新申请状态
    getList()
  } catch (error) {
    console.error('提交申请失败:', error)
    proxy?.$modal?.msgError('提交申请失败')
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  applyForm.policyId = ''
  applyForm.applicantName = ''
  applyForm.applicantPhone = ''
  applyForm.companyName = ''
  applyForm.companyCode = ''
  applyForm.companyLegalPerson = ''
  applyForm.companyAddress = ''
  applyForm.companyContactPerson = ''
  applyForm.companyContactPhone = ''
  applyForm.bankName = ''
  applyForm.bankAccountName = ''
  applyForm.bankAccountNumber = ''
  applyForm.remark = ''
  resetMaterials()
  if (applyFormRef.value) {
    applyFormRef.value.resetFields()
  }
}
</script>

<style lang="scss" scoped>
.policy-container {
  .loading-placeholder {
    text-align: center;
    padding: 50px;
  }
}

.operation-btns {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 申请弹窗样式 */
.apply-form {
  max-height: 70vh;
  overflow-y: auto;
}

.required-materials {
  margin-top: 10px;
}

.material-item {
  display: flex;
  flex-direction: column;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 16px;
  background-color: #fafafa;
  gap: 12px;
}

.material-header {
  width: 100%;
}

.material-info {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.material-icon {
  color: #409eff;
  font-size: 18px;
  flex-shrink: 0;
}

.material-name {
  color: #303133;
  font-weight: 500;
  flex: 1;
  line-height: 1.4;
}

.material-tags {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.material-upload {
  width: 100%;
  padding-left: 30px;
}

.dialog-footer {
  text-align: right;
}

/* 滚动条样式 */
.apply-form::-webkit-scrollbar {
  width: 6px;
}

.apply-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.apply-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.apply-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>